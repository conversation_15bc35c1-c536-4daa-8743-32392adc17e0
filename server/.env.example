# Database Configuration
DATABASE_URL="postgresql://username:password@host:port/database?sslmode=require"

# Authentication Configuration
BETTER_AUTH_SECRET="your-secret-key-here"
BETTER_AUTH_URL="https://your-backend-url.com"
BETTER_AUTH_TRUSTED_ORIGINS="https://your-frontend-url.com"

# Xendit Payment Configuration
XENDIT_API_KEY="your-xendit-api-key"
XENDIT_API_URL="https://api.xendit.co/v2/invoices"
XENDIT_CALLBACK_TOKEN="your-xendit-callback-token"

# Server Configuration
PORT=8080

# CORS Configuration
CORS_ORIGINS="http://localhost:3001,https://your-frontend-url.com"
CORS_ALLOW_HEADERS="Content-Type,Authorization,X-Session-Token"
CORS_ALLOW_METHODS="GET,POST,PUT,DELETE,OPTIONS"

# Application Configuration
APP_NAME="your-app-name"
EXTERNAL_ID_PREFIX="your-prefix-"
