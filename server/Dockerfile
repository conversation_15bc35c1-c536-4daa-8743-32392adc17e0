FROM oven/bun:1

WORKDIR /app

# Install dependencies
RUN apt-get update && apt-get install -y openssl && rm -rf /var/lib/apt/lists/*

# Copy server package files first
COPY server/package.json ./server/
COPY server/bun.lock ./server/ 2>/dev/null || true

# Copy shared package
COPY shared/ ./shared/

# Copy server source
COPY server/ ./server/

# Install dependencies in server directory
WORKDIR /app/server
RUN bun install

# Go back to app root
WORKDIR /app

# Build shared package first
RUN cd shared && bun run build

# Generate Prisma client
RUN cd server && bunx prisma generate

# Build the server app
RUN cd server && bun run build

# Copy and set permissions for entrypoint script
COPY server/docker-entrypoint /usr/local/bin/
RUN chmod +x /usr/local/bin/docker-entrypoint

# Expose port
EXPOSE 8080

# Use entrypoint script
ENTRYPOINT ["docker-entrypoint"]
CMD ["bun", "run", "start"]
