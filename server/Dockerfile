FROM oven/bun:1

WORKDIR /app

# Install dependencies
RUN apt-get update && apt-get install -y openssl && rm -rf /var/lib/apt/lists/*

# Copy root package files and workspace configuration
COPY package.json bun.lock ./
COPY tsconfig.json ./

# Copy shared package
COPY shared/ ./shared/

# Copy server package
COPY server/ ./server/

# Install dependencies
RUN bun install

# Build shared package first
RUN cd shared && bun run build

# Generate Prisma client
RUN cd server && bunx prisma generate

# Build the server app
RUN cd server && bun run build

# Copy and set permissions for entrypoint script
COPY server/docker-entrypoint /usr/local/bin/
RUN chmod +x /usr/local/bin/docker-entrypoint

# Expose port
EXPOSE 8080

# Use entrypoint script
ENTRYPOINT ["docker-entrypoint"]
CMD ["bun", "run", "start"]
