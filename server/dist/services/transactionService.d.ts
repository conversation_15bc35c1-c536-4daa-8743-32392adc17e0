export declare class TransactionService {
    static getTransactions(userId: string, page?: number, limit?: number): Promise<{
        transactions: {
            id: string;
            amount: number;
            type: string;
            date: Date;
        }[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
        };
    }>;
    static getRecentTransactions(userId: string, limit?: number): Promise<{
        id: string;
        amount: number;
        type: string;
        date: Date;
    }[]>;
    static getTransactionCount(userId: string): Promise<number>;
}
