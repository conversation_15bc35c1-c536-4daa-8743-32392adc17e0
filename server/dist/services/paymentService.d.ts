export declare class PaymentService {
    static getPayments(userId: string, page?: number, limit?: number): Promise<{
        payments: {
            id: number;
            createdAt: Date;
            amount: number;
            status: string | null;
            invoiceUrl: string | null;
        }[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
        };
    }>;
    static getPaymentCount(userId: string): Promise<number>;
}
