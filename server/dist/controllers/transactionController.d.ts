import type { Context } from "hono";
import type { Variables } from "../../../shared/dist/index.js";
export declare class TransactionController {
    static getTransactions(c: Context<{
        Variables: Variables;
    }>): Promise<Response & import("hono").TypedResponse<{
        transactions: {
            id: string;
            amount: number;
            type: string;
            date: string;
        }[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
        };
    }, import("hono/utils/http-status").ContentfulStatusCode, "json">>;
}
