import type { Context } from "hono";
import type { Variables } from "../../../shared/dist/index.js";
export declare class PaymentController {
    static getPayments(c: Context<{
        Variables: Variables;
    }>): Promise<Response & import("hono").TypedResponse<{
        payments: {
            id: number;
            createdAt: string;
            amount: number;
            status: string | null;
            invoiceUrl: string | null;
        }[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
        };
    }, import("hono/utils/http-status").ContentfulStatusCode, "json">>;
}
