import type { Context } from "hono";
import type { Variables } from "../../../shared/dist/index.js";
export declare class BalanceController {
    static getBalance(c: Context<{
        Variables: Variables;
    }>): Promise<Response & import("hono").TypedResponse<{
        id: string;
        userBalance: number | null;
        createdAt: string;
    } | {
        userBalance: number;
        createdAt: string;
    }, import("hono/utils/http-status").ContentfulStatusCode, "json">>;
}
