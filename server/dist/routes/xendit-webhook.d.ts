import type { Context } from "hono";
export declare const xenditWebhook: (c: Context) => Promise<(Response & import("hono").TypedResponse<{
    message: string;
}, 401, "json">) | (Response & import("hono").TypedResponse<{
    message: string;
}, 400, "json">) | (Response & import("hono").TypedResponse<{
    message: string;
}, 404, "json">) | (Response & import("hono").TypedResponse<{
    status: string;
}, import("hono/utils/http-status").ContentfulStatusCode, "json">)>;
