{"name": "rou3", "version": "0.5.1", "description": "Lightweight and fast router for JavaScript based on Radix Tree", "repository": "unjs/rou3", "license": "MIT", "sideEffects": false, "type": "module", "exports": {".": {"types": "./dist/index.d.ts", "import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.cts", "files": ["dist"], "scripts": {"bench:bun": "bun ./test/bench", "bench:node": "node --import jiti/register ./test/bench", "build": "unbuild", "dev": "vitest", "lint": "eslint . && prettier -c src test", "lint:fix": "automd && eslint --fix . && prettier -w src test", "release": "pnpm test && pnpm build && changelogen --release && git push --follow-tags && npm publish", "test": "pnpm lint && pnpm test:types && vitest run --coverage", "test:types": "tsc --noEmit"}, "devDependencies": {"0x": "^5.7.0", "@medley/router": "^0.2.1", "@vitest/coverage-v8": "^2.0.3", "automd": "^0.3.8", "changelogen": "^0.5.5", "esbuild": "^0.23.0", "eslint": "^9.7.0", "eslint-config-unjs": "^0.3.2", "find-my-way": "^8.2.0", "hono": "^4.5.0", "jiti": "^2.0.0-beta.3", "koa-tree-router": "^0.12.1", "listhen": "^1.7.2", "mitata": "^0.1.11", "prettier": "^3.3.3", "radix3": "^1.1.2", "rou3-release": "npm:rou3@0.4.0", "typescript": "^5.5.3", "unbuild": "^3.0.0-rc.7", "vitest": "^2.0.3"}, "packageManager": "pnpm@9.5.0"}