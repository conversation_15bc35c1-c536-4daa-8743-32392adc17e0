{"name": "nanostores", "version": "0.11.4", "description": "A tiny (265 bytes) state manager for React/Preact/Vue/Svelte with many atomic tree-shakable stores", "keywords": ["store", "state", "state manager", "react", "react native", "preact", "vue", "svelte"], "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "repository": "nanostores/nanostores", "sideEffects": false, "type": "module", "types": "./index.d.ts", "exports": {".": "./index.js", "./package.json": "./package.json"}, "engines": {"node": "^18.0.0 || >=20.0.0"}, "funding": [{"type": "github", "url": "https://github.com/sponsors/ai"}]}