# isows

Isomorphic WebSocket implementation for Node.js, Bun, Deno, and modern browsers.

## Install

```bash
npm i isows
```

```bash
pnpm i isows
```

```bash
bun i isows
```

## Usage

```ts
import { WebSocket } from 'isows'

const ws = new WebSocket('ws://localhost:8080')
```

## Authors

- [@jxom](https://github.com/jxom) (jxom.eth, [Twitter](https://twitter.com/_jxom))

## License

[MIT](/LICENSE) License
