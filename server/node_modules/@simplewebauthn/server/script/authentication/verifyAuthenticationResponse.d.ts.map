{"version": 3, "file": "verifyAuthenticationResponse.d.ts", "sourceRoot": "", "sources": ["../../src/authentication/verifyAuthenticationResponse.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EACV,0BAA0B,EAC1B,eAAe,EACf,oBAAoB,EACpB,2BAA2B,EAC3B,kBAAkB,EACnB,MAAM,mBAAmB,CAAC;AAM3B,OAAO,KAAK,EAAE,4CAA4C,EAAE,MAAM,6CAA6C,CAAC;AAIhH;;GAEG;AACH,MAAM,MAAM,gCAAgC,GAAG,UAAU,CAAC,OAAO,4BAA4B,CAAC,CAAC,CAAC,CAAC,CAAC;AAElG;;;;;;;;;;;;;;GAcG;AACH,wBAAsB,4BAA4B,CAChD,OAAO,EAAE;IACP,QAAQ,EAAE,0BAA0B,CAAC;IACrC,iBAAiB,EAAE,MAAM,GAAG,CAAC,CAAC,SAAS,EAAE,MAAM,KAAK,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC;IAChF,cAAc,EAAE,MAAM,GAAG,MAAM,EAAE,CAAC;IAClC,YAAY,EAAE,MAAM,GAAG,MAAM,EAAE,CAAC;IAChC,UAAU,EAAE,kBAAkB,CAAC;IAC/B,YAAY,CAAC,EAAE,MAAM,GAAG,MAAM,EAAE,CAAC;IACjC,uBAAuB,CAAC,EAAE,OAAO,CAAC;IAClC,kBAAkB,CAAC,EAAE;QACnB,gBAAgB,CAAC,EAAE,2BAA2B,CAAC;KAChD,CAAC;CACH,GACA,OAAO,CAAC,8BAA8B,CAAC,CAmNzC;AAED;;;;;;;;;;;;;;;;;;;GAmBG;AACH,MAAM,MAAM,8BAA8B,GAAG;IAC3C,QAAQ,EAAE,OAAO,CAAC;IAClB,kBAAkB,EAAE;QAClB,YAAY,EAAE,eAAe,CAAC;QAC9B,UAAU,EAAE,MAAM,CAAC;QACnB,YAAY,EAAE,OAAO,CAAC;QACtB,oBAAoB,EAAE,oBAAoB,CAAC;QAC3C,kBAAkB,EAAE,OAAO,CAAC;QAC5B,MAAM,EAAE,MAAM,CAAC;QACf,IAAI,EAAE,MAAM,CAAC;QACb,6BAA6B,CAAC,EAAE,4CAA4C,CAAC;KAC9E,CAAC;CACH,CAAC"}