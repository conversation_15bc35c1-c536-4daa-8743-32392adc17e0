{"version": 3, "file": "cose.d.ts", "sourceRoot": "", "sources": ["../../src/helpers/cose.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;GAUG;AAEH;;GAEG;AACH,MAAM,MAAM,aAAa,GAAG;IAE1B,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,GAAG,GAAG,OAAO,GAAG,SAAS,CAAC;IAC5C,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,GAAG,GAAG,OAAO,GAAG,SAAS,CAAC;IAE5C,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,GAAG,EAAE,KAAK,EAAE,OAAO,GAAG,IAAI,CAAC;IAC7C,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,GAAG,EAAE,KAAK,EAAE,OAAO,GAAG,IAAI,CAAC;CAC9C,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,gBAAgB,GAAG,aAAa,GAAG;IAE7C,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,GAAG,GAAG,MAAM,GAAG,SAAS,CAAC;IAC3C,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC,GAAG,UAAU,GAAG,SAAS,CAAC;IAE7C,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,GAAG,IAAI,CAAC;IAC5C,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC,EAAE,KAAK,EAAE,UAAU,GAAG,IAAI,CAAC;CAC/C,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,gBAAgB,GAAG,aAAa,GAAG;IAE7C,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,GAAG,GAAG,MAAM,GAAG,SAAS,CAAC;IAC3C,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC,GAAG,UAAU,GAAG,SAAS,CAAC;IAC7C,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC,GAAG,UAAU,GAAG,SAAS,CAAC;IAE7C,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,GAAG,IAAI,CAAC;IAC5C,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC,EAAE,KAAK,EAAE,UAAU,GAAG,IAAI,CAAC;IAC9C,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC,EAAE,KAAK,EAAE,UAAU,GAAG,IAAI,CAAC;CAC/C,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,gBAAgB,GAAG,aAAa,GAAG;IAE7C,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC,GAAG,UAAU,GAAG,SAAS,CAAC;IAC7C,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC,GAAG,UAAU,GAAG,SAAS,CAAC;IAE7C,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC,EAAE,KAAK,EAAE,UAAU,GAAG,IAAI,CAAC;IAC9C,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC,EAAE,KAAK,EAAE,UAAU,GAAG,IAAI,CAAC;CAC/C,CAAC;AAEF;;GAEG;AACH,wBAAgB,kBAAkB,CAChC,aAAa,EAAE,aAAa,GAC3B,aAAa,IAAI,gBAAgB,CAGnC;AAED;;GAEG;AACH,wBAAgB,kBAAkB,CAChC,aAAa,EAAE,aAAa,GAC3B,aAAa,IAAI,gBAAgB,CAGnC;AAED;;GAEG;AACH,wBAAgB,kBAAkB,CAChC,aAAa,EAAE,aAAa,GAC3B,aAAa,IAAI,gBAAgB,CAGnC;AAED;;;;;GAKG;AACH,oBAAY,QAAQ;IAClB,GAAG,IAAI;IACP,GAAG,IAAI;IACP,GAAG,KAAK;IACR,CAAC,KAAK;IACN,CAAC,KAAK;IACN,CAAC,KAAK;IACN,CAAC,KAAK;CACP;AAED;;;;GAIG;AACH,oBAAY,OAAO;IACjB,GAAG,IAAI;IACP,GAAG,IAAI;IACP,GAAG,IAAI;CACR;AAED,wBAAgB,SAAS,CAAC,GAAG,EAAE,MAAM,GAAG,SAAS,GAAG,GAAG,IAAI,OAAO,CAEjE;AAED;;;;GAIG;AACH,oBAAY,OAAO;IACjB,IAAI,IAAI;IACR,IAAI,IAAI;IACR,IAAI,IAAI;IACR,OAAO,IAAI;IACX,SAAS,IAAI;CACd;AAED,wBAAgB,SAAS,CAAC,GAAG,EAAE,MAAM,GAAG,SAAS,GAAG,GAAG,IAAI,OAAO,CAEjE;AAED;;;;GAIG;AACH,oBAAY,OAAO;IACjB,KAAK,KAAK;IACV,KAAK,KAAK;IACV,KAAK,MAAM;IACX,KAAK,MAAM;IACX,KAAK,MAAM;IACX,KAAK,MAAM;IACX,KAAK,MAAM;IACX,MAAM,MAAM;IACZ,KAAK,OAAO;IACZ,KAAK,OAAO;IACZ,KAAK,OAAO;IACZ,GAAG,SAAS;CACb;AAED,wBAAgB,SAAS,CAAC,GAAG,EAAE,MAAM,GAAG,SAAS,GAAG,GAAG,IAAI,OAAO,CAEjE"}