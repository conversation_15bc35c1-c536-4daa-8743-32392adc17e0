import { COSEPublic<PERSON>ey } from './cose.js';
export declare function decodeCredentialPublicKey(publicKey: Uint8Array): COSEPublicKey;
/**
 * Make it possible to stub the return value during testing
 * @ignore Don't include this in docs output
 */
export declare const _decodeCredentialPublicKeyInternals: {
    stubThis: (value: COSEPublicKey) => COSEPublicKey;
};
//# sourceMappingURL=decodeCredentialPublicKey.d.ts.map