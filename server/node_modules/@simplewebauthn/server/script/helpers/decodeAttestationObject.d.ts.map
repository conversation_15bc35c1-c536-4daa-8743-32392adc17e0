{"version": 3, "file": "decodeAttestationObject.d.ts", "sourceRoot": "", "sources": ["../../src/helpers/decodeAttestationObject.ts"], "names": [], "mappings": "AAEA;;;;GAIG;AACH,wBAAgB,uBAAuB,CACrC,iBAAiB,EAAE,UAAU,GAC5B,iBAAiB,CAInB;AAED,MAAM,MAAM,iBAAiB,GACzB,UAAU,GACV,QAAQ,GACR,mBAAmB,GACnB,aAAa,GACb,KAAK,GACL,OAAO,GACP,MAAM,CAAC;AAEX,MAAM,MAAM,iBAAiB,GAAG;IAC9B,GAAG,CAAC,GAAG,EAAE,KAAK,GAAG,iBAAiB,CAAC;IACnC,GAAG,CAAC,GAAG,EAAE,SAAS,GAAG,oBAAoB,CAAC;IAC1C,GAAG,CAAC,GAAG,EAAE,UAAU,GAAG,UAAU,CAAC;CAClC,CAAC;AAEF;;;GAGG;AACH,MAAM,MAAM,oBAAoB,GAAG;IACjC,GAAG,CAAC,GAAG,EAAE,KAAK,GAAG,UAAU,GAAG,SAAS,CAAC;IACxC,GAAG,CAAC,GAAG,EAAE,KAAK,GAAG,UAAU,EAAE,GAAG,SAAS,CAAC;IAC1C,GAAG,CAAC,GAAG,EAAE,UAAU,GAAG,UAAU,GAAG,SAAS,CAAC;IAC7C,GAAG,CAAC,GAAG,EAAE,KAAK,GAAG,MAAM,GAAG,SAAS,CAAC;IACpC,GAAG,CAAC,GAAG,EAAE,KAAK,GAAG,MAAM,GAAG,SAAS,CAAC;IACpC,GAAG,CAAC,GAAG,EAAE,UAAU,GAAG,UAAU,GAAG,SAAS,CAAC;IAC7C,GAAG,CAAC,GAAG,EAAE,SAAS,GAAG,UAAU,GAAG,SAAS,CAAC;IAE5C,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC;CACvB,CAAC;AAEF;;;GAGG;AACH,eAAO,MAAM,iCAAiC;sBAC1B,iBAAiB;CACpC,CAAC"}