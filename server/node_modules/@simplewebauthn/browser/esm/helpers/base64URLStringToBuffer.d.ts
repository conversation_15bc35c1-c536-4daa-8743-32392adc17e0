/**
 * Convert from a Base64URL-encoded string to an Array Buffer. Best used when converting a
 * credential ID from a JSON string to an ArrayBuffer, like in allowCredentials or
 * excludeCredentials
 *
 * Helper method to compliment `bufferToBase64URLString`
 */
export declare function base64URLStringToBuffer(base64URLString: string): ArrayBuffer;
//# sourceMappingURL=base64URLStringToBuffer.d.ts.map