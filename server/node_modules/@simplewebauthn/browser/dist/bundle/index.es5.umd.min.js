/* [@simplewebauthn/browser@13.1.2] */
function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}!function(e,t){if("function"==typeof define&&define.amd)define("SimpleWebAuthnBrowser",["exports"],t);else if("undefined"!=typeof exports)t(exports);else{var r={exports:{}};t(r.exports),e.SimpleWebAuthnBrowser=r.exports}}("undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:this,(function(t){function r(){r=function(){return n};var t,n={},o=Object.prototype,i=o.hasOwnProperty,a=Object.defineProperty||function(e,t,r){e[t]=r.value},u="function"==typeof Symbol?Symbol:{},c=u.iterator||"@@iterator",s=u.asyncIterator||"@@asyncIterator",l=u.toStringTag||"@@toStringTag";function f(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{f({},"")}catch(t){f=function(e,t,r){return e[t]=r}}function p(e,t,r,n){var o=t&&t.prototype instanceof g?t:g,i=Object.create(o.prototype),u=new j(n||[]);return a(i,"_invoke",{value:T(e,r,u)}),i}function h(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}n.wrap=p;var d="suspendedStart",y="suspendedYield",b="executing",v="completed",w={};function g(){}function m(){}function E(){}var R={};f(R,c,(function(){return this}));var A=Object.getPrototypeOf,O=A&&A(A(L([])));O&&O!==o&&i.call(O,c)&&(R=O);var _=E.prototype=g.prototype=Object.create(R);function S(e){["next","throw","return"].forEach((function(t){f(e,t,(function(e){return this._invoke(t,e)}))}))}function P(t,r){function n(o,a,u,c){var s=h(t[o],t,a);if("throw"!==s.type){var l=s.arg,f=l.value;return f&&"object"==e(f)&&i.call(f,"__await")?r.resolve(f.__await).then((function(e){n("next",e,u,c)}),(function(e){n("throw",e,u,c)})):r.resolve(f).then((function(e){l.value=e,u(l)}),(function(e){return n("throw",e,u,c)}))}c(s.arg)}var o;a(this,"_invoke",{value:function(e,t){function i(){return new r((function(r,o){n(e,t,r,o)}))}return o=o?o.then(i,i):i()}})}function T(e,r,n){var o=d;return function(i,a){if(o===b)throw Error("Generator is already running");if(o===v){if("throw"===i)throw a;return{value:t,done:!0}}for(n.method=i,n.arg=a;;){var u=n.delegate;if(u){var c=I(u,n);if(c){if(c===w)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===d)throw o=v,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=b;var s=h(e,r,n);if("normal"===s.type){if(o=n.done?v:y,s.arg===w)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(o=v,n.method="throw",n.arg=s.arg)}}}function I(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,I(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),w;var i=h(o,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,w;var a=i.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,w):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,w)}function x(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function N(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function j(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(x,this),this.reset(!0)}function L(r){if(r||""===r){var n=r[c];if(n)return n.call(r);if("function"==typeof r.next)return r;if(!isNaN(r.length)){var o=-1,a=function e(){for(;++o<r.length;)if(i.call(r,o))return e.value=r[o],e.done=!1,e;return e.value=t,e.done=!0,e};return a.next=a}}throw new TypeError(e(r)+" is not iterable")}return m.prototype=E,a(_,"constructor",{value:E,configurable:!0}),a(E,"constructor",{value:m,configurable:!0}),m.displayName=f(E,l,"GeneratorFunction"),n.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===m||"GeneratorFunction"===(t.displayName||t.name))},n.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,E):(e.__proto__=E,f(e,l,"GeneratorFunction")),e.prototype=Object.create(_),e},n.awrap=function(e){return{__await:e}},S(P.prototype),f(P.prototype,s,(function(){return this})),n.AsyncIterator=P,n.async=function(e,t,r,o,i){void 0===i&&(i=Promise);var a=new P(p(e,t,r,o),i);return n.isGeneratorFunction(t)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},S(_),f(_,l,"Generator"),f(_,c,(function(){return this})),f(_,"toString",(function(){return"[object Generator]"})),n.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},n.values=L,j.prototype={constructor:j,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(N),!e)for(var r in this)"t"===r.charAt(0)&&i.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function n(n,o){return u.type="throw",u.arg=e,r.next=n,o&&(r.method="next",r.arg=t),!!o}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],u=a.completion;if("root"===a.tryLoc)return n("end");if(a.tryLoc<=this.prev){var c=i.call(a,"catchLoc"),s=i.call(a,"finallyLoc");if(c&&s){if(this.prev<a.catchLoc)return n(a.catchLoc,!0);if(this.prev<a.finallyLoc)return n(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return n(a.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return n(a.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&i.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var o=n;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=e,a.arg=t,o?(this.method="next",this.next=o.finallyLoc,w):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),w},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),N(r),w}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var o=n.arg;N(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:L(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),w}},n}function n(e,t,r,n,o,i,a){try{var u=e[i](a),c=u.value}catch(e){return void r(e)}u.done?t(c):Promise.resolve(c).then(n,o)}function o(e){return function(){var t=this,r=arguments;return new Promise((function(o,i){var a=e.apply(t,r);function u(e){n(a,o,i,u,c,"next",e)}function c(e){n(a,o,i,u,c,"throw",e)}u(void 0)}))}}function i(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,b(n.key),n)}}function a(e,t,r){return t&&i(e.prototype,t),r&&i(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function u(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function c(t,r,n){return r=p(r),function(t,r){if(r&&("object"==e(r)||"function"==typeof r))return r;if(void 0!==r)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(t)}(t,l()?Reflect.construct(r,n||[],p(t).constructor):r.apply(t,n))}function s(e){var t="function"==typeof Map?new Map:void 0;return s=function(e){if(null===e||!function(e){try{return-1!==Function.toString.call(e).indexOf("[native code]")}catch(t){return"function"==typeof e}}(e))return e;if("function"!=typeof e)throw new TypeError("Super expression must either be null or a function");if(void 0!==t){if(t.has(e))return t.get(e);t.set(e,r)}function r(){return function(e,t,r){if(l())return Reflect.construct.apply(null,arguments);var n=[null];n.push.apply(n,t);var o=new(e.bind.apply(e,n));return r&&f(o,r.prototype),o}(e,arguments,p(this).constructor)}return r.prototype=Object.create(e.prototype,{constructor:{value:r,enumerable:!1,writable:!0,configurable:!0}}),f(r,e)},s(e)}function l(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(l=function(){return!!e})()}function f(e,t){return f=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},f(e,t)}function p(e){return p=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},p(e)}function h(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function d(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?h(Object(r),!0).forEach((function(t){y(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):h(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function y(e,t,r){return(t=b(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function b(t){var r=function(t,r){if("object"!=e(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var o=n.call(t,r||"default");if("object"!=e(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(t)}(t,"string");return"symbol"==e(r)?r:r+""}function v(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=function(e,t){if(e){if("string"==typeof e)return w(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?w(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,u=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return a=e.done,e},e:function(e){u=!0,i=e},f:function(){try{a||null==r.return||r.return()}finally{if(u)throw i}}}}function w(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function g(e){var t,r="",n=v(new Uint8Array(e));try{for(n.s();!(t=n.n()).done;){var o=t.value;r+=String.fromCharCode(o)}}catch(e){n.e(e)}finally{n.f()}return btoa(r).replace(/\+/g,"-").replace(/\//g,"_").replace(/=/g,"")}function m(e){for(var t=e.replace(/-/g,"+").replace(/_/g,"/"),r=(4-t.length%4)%4,n=t.padEnd(t.length+r,"="),o=atob(n),i=new ArrayBuffer(o.length),a=new Uint8Array(i),u=0;u<o.length;u++)a[u]=o.charCodeAt(u);return i}function E(){var e;return R.stubThis(void 0!==(null===(e=window)||void 0===e?void 0:e.PublicKeyCredential)&&"function"==typeof window.PublicKeyCredential)}Object.defineProperty(t,"__esModule",{value:!0}),t._browserSupportsWebAuthnInternals=t._browserSupportsWebAuthnAutofillInternals=t.WebAuthnError=t.WebAuthnAbortService=void 0,t.base64URLStringToBuffer=m,t.browserSupportsWebAuthn=E,t.browserSupportsWebAuthnAutofill=L,t.bufferToBase64URLString=g,t.platformAuthenticatorIsAvailable=function(){if(!E())return new Promise((function(e){return e(!1)}));return PublicKeyCredential.isUserVerifyingPlatformAuthenticatorAvailable()},t.startAuthentication=function(e){return U.apply(this,arguments)},t.startRegistration=function(e){return N.apply(this,arguments)};var R=t._browserSupportsWebAuthnInternals={stubThis:function(e){return e}};function A(e){var t=e.id;return d(d({},e),{},{id:m(t),transports:e.transports})}function O(e){return"localhost"===e||/^([a-z0-9]+(-[a-z0-9]+)*\.)+[a-z]{2,}$/i.test(e)}var _=t.WebAuthnError=function(e){function t(e){var r,n=e.message,o=e.code,i=e.cause,a=e.name;return u(this,t),r=c(this,t,[n,{cause:i}]),Object.defineProperty(r,"code",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),r.name=null!=a?a:i.name,r.code=o,r}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&f(e,t)}(t,e),a(t)}(s(Error));function S(e){var t=e.error,r=e.options,n=r.publicKey;if(!n)throw Error("options was missing required publicKey property");if("AbortError"===t.name){if(r.signal instanceof AbortSignal)return new _({message:"Registration ceremony was sent an abort signal",code:"ERROR_CEREMONY_ABORTED",cause:t})}else if("ConstraintError"===t.name){var o,i,a;if(!0===(null===(o=n.authenticatorSelection)||void 0===o?void 0:o.requireResidentKey))return new _({message:"Discoverable credentials were required but no available authenticator supported it",code:"ERROR_AUTHENTICATOR_MISSING_DISCOVERABLE_CREDENTIAL_SUPPORT",cause:t});if("conditional"===r.mediation&&"required"===(null===(i=n.authenticatorSelection)||void 0===i?void 0:i.userVerification))return new _({message:"User verification was required during automatic registration but it could not be performed",code:"ERROR_AUTO_REGISTER_USER_VERIFICATION_FAILURE",cause:t});if("required"===(null===(a=n.authenticatorSelection)||void 0===a?void 0:a.userVerification))return new _({message:"User verification was required but no available authenticator supported it",code:"ERROR_AUTHENTICATOR_MISSING_USER_VERIFICATION_SUPPORT",cause:t})}else{if("InvalidStateError"===t.name)return new _({message:"The authenticator was previously registered",code:"ERROR_AUTHENTICATOR_PREVIOUSLY_REGISTERED",cause:t});if("NotAllowedError"===t.name)return new _({message:t.message,code:"ERROR_PASSTHROUGH_SEE_CAUSE_PROPERTY",cause:t});if("NotSupportedError"===t.name)return 0===n.pubKeyCredParams.filter((function(e){return"public-key"===e.type})).length?new _({message:'No entry in pubKeyCredParams was of type "public-key"',code:"ERROR_MALFORMED_PUBKEYCREDPARAMS",cause:t}):new _({message:"No available authenticator supported any of the specified pubKeyCredParams algorithms",code:"ERROR_AUTHENTICATOR_NO_SUPPORTED_PUBKEYCREDPARAMS_ALG",cause:t});if("SecurityError"===t.name){var u=window.location.hostname;if(!O(u))return new _({message:"".concat(window.location.hostname," is an invalid domain"),code:"ERROR_INVALID_DOMAIN",cause:t});if(n.rp.id!==u)return new _({message:'The RP ID "'.concat(n.rp.id,'" is invalid for this domain'),code:"ERROR_INVALID_RP_ID",cause:t})}else if("TypeError"===t.name){if(n.user.id.byteLength<1||n.user.id.byteLength>64)return new _({message:"User ID was not between 1 and 64 characters",code:"ERROR_INVALID_USER_ID_LENGTH",cause:t})}else if("UnknownError"===t.name)return new _({message:"The authenticator was unable to process the specified options, or could not create a new credential",code:"ERROR_AUTHENTICATOR_GENERAL_ERROR",cause:t})}return t}var P=function(){return a((function e(){u(this,e),Object.defineProperty(this,"controller",{enumerable:!0,configurable:!0,writable:!0,value:void 0})}),[{key:"createNewAbortSignal",value:function(){if(this.controller){var e=new Error("Cancelling existing WebAuthn API call for new one");e.name="AbortError",this.controller.abort(e)}var t=new AbortController;return this.controller=t,t.signal}},{key:"cancelCeremony",value:function(){if(this.controller){var e=new Error("Manually cancelling existing WebAuthn API call");e.name="AbortError",this.controller.abort(e),this.controller=void 0}}}])}(),T=t.WebAuthnAbortService=new P,I=["cross-platform","platform"];function x(e){if(e&&!(I.indexOf(e)<0))return e}function N(){return(N=o(r().mark((function e(t){var n,o,i,a,u,c,s,l,f,p,h,y,b,v,w,R,O,_;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!t.optionsJSON&&t.challenge&&(console.warn("startRegistration() was not called correctly. It will try to continue with the provided options, but this call should be refactored to use the expected call structure instead. See https://simplewebauthn.dev/docs/packages/browser#typeerror-cannot-read-properties-of-undefined-reading-challenge for more information."),t={optionsJSON:t}),i=(o=t).optionsJSON,a=o.useAutoRegister,u=void 0!==a&&a,E()){e.next=4;break}throw new Error("WebAuthn is not supported in this browser");case 4:return c=d(d({},i),{},{challenge:m(i.challenge),user:d(d({},i.user),{},{id:m(i.user.id)}),excludeCredentials:null===(n=i.excludeCredentials)||void 0===n?void 0:n.map(A)}),s={},u&&(s.mediation="conditional"),s.publicKey=c,s.signal=T.createNewAbortSignal(),e.prev=9,e.next=12,navigator.credentials.create(s);case 12:l=e.sent,e.next=18;break;case 15:throw e.prev=15,e.t0=e.catch(9),S({error:e.t0,options:s});case 18:if(l){e.next=20;break}throw new Error("Registration was not completed");case 20:if(p=(f=l).id,h=f.rawId,y=f.response,b=f.type,v=void 0,"function"==typeof y.getTransports&&(v=y.getTransports()),w=void 0,"function"==typeof y.getPublicKeyAlgorithm)try{w=y.getPublicKeyAlgorithm()}catch(e){j("getPublicKeyAlgorithm()",e)}if(R=void 0,"function"==typeof y.getPublicKey)try{null!==(O=y.getPublicKey())&&(R=g(O))}catch(e){j("getPublicKey()",e)}if("function"==typeof y.getAuthenticatorData)try{_=g(y.getAuthenticatorData())}catch(e){j("getAuthenticatorData()",e)}return e.abrupt("return",{id:p,rawId:g(h),response:{attestationObject:g(y.attestationObject),clientDataJSON:g(y.clientDataJSON),transports:v,publicKeyAlgorithm:w,publicKey:R,authenticatorData:_},type:b,clientExtensionResults:l.getClientExtensionResults(),authenticatorAttachment:x(l.authenticatorAttachment)});case 29:case"end":return e.stop()}}),e,null,[[9,15]])})))).apply(this,arguments)}function j(e,t){console.warn("The browser extension that intercepted this WebAuthn API call incorrectly implemented ".concat(e,". You should report this error to them.\n"),t)}function L(){if(!E())return C.stubThis(new Promise((function(e){return e(!1)})));var e=window.PublicKeyCredential;return void 0===(null==e?void 0:e.isConditionalMediationAvailable)?C.stubThis(new Promise((function(e){return e(!1)}))):C.stubThis(e.isConditionalMediationAvailable())}var C=t._browserSupportsWebAuthnAutofillInternals={stubThis:function(e){return e}};function D(e){var t=e.error,r=e.options,n=r.publicKey;if(!n)throw Error("options was missing required publicKey property");if("AbortError"===t.name){if(r.signal instanceof AbortSignal)return new _({message:"Authentication ceremony was sent an abort signal",code:"ERROR_CEREMONY_ABORTED",cause:t})}else{if("NotAllowedError"===t.name)return new _({message:t.message,code:"ERROR_PASSTHROUGH_SEE_CAUSE_PROPERTY",cause:t});if("SecurityError"===t.name){var o=window.location.hostname;if(!O(o))return new _({message:"".concat(window.location.hostname," is an invalid domain"),code:"ERROR_INVALID_DOMAIN",cause:t});if(n.rpId!==o)return new _({message:'The RP ID "'.concat(n.rpId,'" is invalid for this domain'),code:"ERROR_INVALID_RP_ID",cause:t})}else if("UnknownError"===t.name)return new _({message:"The authenticator was unable to process the specified options, or could not create a new assertion signature",code:"ERROR_AUTHENTICATOR_GENERAL_ERROR",cause:t})}return t}function U(){return(U=o(r().mark((function e(t){var n,o,i,a,u,c,s,l,f,p,h,y,b,v,w,R,O,_;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!t.optionsJSON&&t.challenge&&(console.warn("startAuthentication() was not called correctly. It will try to continue with the provided options, but this call should be refactored to use the expected call structure instead. See https://simplewebauthn.dev/docs/packages/browser#typeerror-cannot-read-properties-of-undefined-reading-challenge for more information."),t={optionsJSON:t}),i=(o=t).optionsJSON,a=o.useBrowserAutofill,u=void 0!==a&&a,c=o.verifyBrowserAutofillInput,s=void 0===c||c,E()){e.next=4;break}throw new Error("WebAuthn is not supported in this browser");case 4:if(0!==(null===(n=i.allowCredentials)||void 0===n?void 0:n.length)&&(l=null===(f=i.allowCredentials)||void 0===f?void 0:f.map(A)),p=d(d({},i),{},{challenge:m(i.challenge),allowCredentials:l}),h={},!u){e.next=17;break}return e.next=10,L();case 10:if(e.sent){e.next=12;break}throw Error("Browser does not support WebAuthn autofill");case 12:if(!(document.querySelectorAll("input[autocomplete$='webauthn']").length<1&&s)){e.next=15;break}throw Error('No <input> with "webauthn" as the only or last value in its `autocomplete` attribute was detected');case 15:h.mediation="conditional",p.allowCredentials=[];case 17:return h.publicKey=p,h.signal=T.createNewAbortSignal(),e.prev=19,e.next=22,navigator.credentials.get(h);case 22:y=e.sent,e.next=28;break;case 25:throw e.prev=25,e.t0=e.catch(19),D({error:e.t0,options:h});case 28:if(y){e.next=30;break}throw new Error("Authentication was not completed");case 30:return v=(b=y).id,w=b.rawId,R=b.response,O=b.type,_=void 0,R.userHandle&&(_=g(R.userHandle)),e.abrupt("return",{id:v,rawId:g(w),response:{authenticatorData:g(R.authenticatorData),clientDataJSON:g(R.clientDataJSON),signature:g(R.signature),userHandle:_},type:O,clientExtensionResults:y.getClientExtensionResults(),authenticatorAttachment:x(y.authenticatorAttachment)});case 34:case"end":return e.stop()}}),e,null,[[19,25]])})))).apply(this,arguments)}}));
