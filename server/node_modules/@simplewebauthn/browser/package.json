{"name": "@simplewebauthn/browser", "version": "13.1.2", "description": "SimpleWebAuthn for Browsers", "keywords": ["typescript", "webauthn", "passkeys", "fido", "umd"], "author": "<PERSON> <<EMAIL>>", "homepage": "https://github.com/MasterKale/SimpleWebAuthn/tree/master/packages/browser#readme", "repository": {"type": "git", "url": "git+https://github.com/MasterKale/SimpleWebAuthn.git", "directory": "packages/browser"}, "license": "MIT", "bugs": {"url": "https://github.com/MasterKale/SimpleWebAuthn/issues"}, "main": "./script/index.js", "module": "./esm/index.js", "exports": {".": {"import": "./esm/index.js", "require": "./script/index.js"}}, "publishConfig": {"access": "public"}, "unpkg": "dist/bundle/index.umd.min.js", "dependencies": {}, "devDependencies": {"@types/node": "^20.9.0"}, "_generatedBy": "dnt@dev"}