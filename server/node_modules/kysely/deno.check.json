{"$schema": "https://deno.land/x/deno/cli/schemas/config-file.v1.json", "compilerOptions": {"types": ["./deno.check.d.ts"]}, "imports": {"better-sqlite3": "npm:better-sqlite3", "kysely": "./dist/esm", "kysely/helpers/mssql": "./dist/esm/helpers/mssql.js", "kysely/helpers/mysql": "./dist/esm/helpers/mysql.js", "kysely/helpers/postgres": "./dist/esm/helpers/postgres.js", "kysely/helpers/sqlite": "./dist/esm/helpers/sqlite.js", "lodash/snakeCase": "npm:lodash/snakeCase", "mysql2": "npm:mysql2", "pg": "npm:pg", "pg-cursor": "npm:pg-cursor", "tarn": "npm:tarn", "tedious": "npm:tedious", "type-editor": "./deno.check.d.ts"}}