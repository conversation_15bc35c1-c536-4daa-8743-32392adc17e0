{"name": "kys<PERSON>", "version": "0.28.2", "description": "Type safe SQL query builder", "repository": {"type": "git", "url": "git://github.com/kysely-org/kysely.git"}, "engines": {"node": ">=18.0.0"}, "main": "dist/cjs/index.js", "module": "dist/esm/index.js", "typesVersions": {"<4.6": {"*": ["outdated-typescript.d.ts"]}}, "exports": {".": {"import": "./dist/esm/index.js", "require": "./dist/cjs/index.js", "default": "./dist/cjs/index.js"}, "./helpers/postgres": {"import": "./dist/esm/helpers/postgres.js", "require": "./dist/cjs/helpers/postgres.js", "default": "./dist/cjs/helpers/postgres.js"}, "./helpers/mysql": {"import": "./dist/esm/helpers/mysql.js", "require": "./dist/cjs/helpers/mysql.js", "default": "./dist/cjs/helpers/mysql.js"}, "./helpers/mssql": {"import": "./dist/esm/helpers/mssql.js", "require": "./dist/cjs/helpers/mssql.js", "default": "./dist/cjs/helpers/mssql.js"}, "./helpers/sqlite": {"import": "./dist/esm/helpers/sqlite.js", "require": "./dist/cjs/helpers/sqlite.js", "default": "./dist/cjs/helpers/sqlite.js"}}, "scripts": {"clean": "rm -rf dist & rm -rf test/node/dist & rm -rf test/browser/bundle.js & rm -rf helpers", "bench:ts": "npm run build && cd ./test/ts-benchmarks && tsx ./index.ts", "test": "npm run build && npm run test:node:build && npm run test:node:run && npm run test:typings && npm run test:esmimports && npm run test:exports", "test:node:build": "tsc -p test/node", "test:node": "npm run build && npm run test:node:build && npm run test:node:run", "test:node:run": "mocha --timeout 15000 test/node/dist/**/*.test.js", "test:browser:build": "rm -rf test/browser/bundle.js && esbuild test/browser/main.ts --bundle --outfile=test/browser/bundle.js", "test:browser": "npm run build && npm run test:browser:build && node test/browser/test.js", "test:bun": "npm run build && bun link && cd test/bun && bun install && bun run test", "test:cloudflare-workers": "npm run build && cd test/cloudflare-workers && npm ci && npm test", "test:deno": "deno run --allow-env --allow-read --allow-net test/deno/local.test.ts && deno run --allow-env --allow-read --allow-net test/deno/cdn.test.ts", "test:typings": "tsd test/typings", "test:esmimports": "node scripts/check-esm-imports.js", "test:esbuild": "esbuild --bundle --platform=node --external:pg-native dist/esm/index.js --outfile=/dev/null", "test:exports": "attw --pack . && node scripts/check-exports.js", "test:jsdocs": "deno check --doc-only --no-lock --unstable-sloppy-imports --config=\"deno.check.json\" ./src", "test:outdatedts": "npm run build && cd test/outdated-ts && npm ci && npm test", "prettier": "prettier --write 'src/**/*.ts' 'test/**/*.ts'", "build": "npm run clean && (npm run build:esm & npm run build:cjs) && npm run script:module-fixup && npm run script:copy-interface-doc", "build:esm": "tsc -p tsconfig.json && npm run script:add-deno-type-references", "build:cjs": "tsc -p tsconfig-cjs.json", "script:module-fixup": "node scripts/module-fixup.js", "script:copy-interface-doc": "node scripts/copy-interface-documentation.js", "script:add-deno-type-references": "node scripts/add-deno-type-references.js", "script:align-site-version": "node scripts/align-site-version.js", "script:generate-site-examples": "node scripts/generate-site-examples.js", "prepublishOnly": "npm run build", "release:preview": "pkg-pr-new publish --template './example'"}, "author": "<PERSON> <<EMAIL>>", "license": "MIT", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON><PERSON> <i<PERSON><PERSON><PERSON><PERSON>@gmail.com>"], "devDependencies": {"@arethetypeswrong/cli": "^0.17.0", "@ark/attest": "^0.36.0", "@types/better-sqlite3": "^7.6.11", "@types/chai": "^4.3.17", "@types/chai-as-promised": "^7.1.8", "@types/chai-subset": "^1.3.5", "@types/mocha": "^10.0.7", "@types/node": "^22.5.0", "@types/pg": "^8.11.6", "@types/pg-cursor": "^2.7.2", "@types/semver": "^7.5.8", "@types/sinon": "^17.0.2", "@types/tedious": "^4.0.9", "better-sqlite3": "^11.2.1", "chai": "^4.5.0", "chai-as-promised": "^7.1.2", "chai-subset": "^1.6.0", "esbuild": "^0.24.0", "lodash": "^4.17.21", "mocha": "^10.7.3", "mysql2": "^3.11.0", "pathe": "^1.1.2", "pg": "^8.12.0", "pg-cursor": "^2.11.0", "pkg-pr-new": "^0.0.30", "playwright": "^1.46.1", "prettier": "^3.3.3", "semver": "^7.6.3", "sinon": "^19.0.2", "tarn": "^3.0.2", "tedious": "^19.0.0", "tsd": "^0.32.0", "tsx": "^4.19.1", "typescript": "^5.6.3"}}