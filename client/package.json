{"name": "client", "version": "0.1.0", "private": true, "type": "module", "dependencies": {"@testing-library/dom": "^10.4.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "better-auth": "^1.2.12", "bootstrap": "^5.3.7", "lucide-react": "^0.525.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-modal": "^3.16.3", "react-router-dom": "^6.30.1", "shared": "workspace:*", "server": "workspace:*", "typescript": "^5.8.3", "web-vitals": "^2.1.4"}, "scripts": {"dev": "bun vite", "start": "bun --bun vite", "build": "bun --bun vite build", "preview": "bun --bun vite preview", "test": "vitest", "test:ui": "vitest --ui", "prepare": "husky"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@biomejs/biome": "2.0.5", "@eslint/js": "^9.30.1", "@testing-library/jest-dom": "^6.6.3", "@types/node": "^24.0.4", "@types/react-modal": "^3.16.3", "@typescript-eslint/eslint-plugin": "^8.36.0", "@typescript-eslint/parser": "^8.36.0", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.21", "eslint": "^9.30.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "husky": "^9.1.7", "jsdom": "^26.1.0", "lint-staged": "^15.5.2", "postcss": "^8.5.6", "prettier": "^3.6.2", "tailwindcss": "^3.0.0", "typescript-eslint": "^8.36.0", "vite": "^5.4.0", "vitest": "^3.2.4"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["prettier --write"]}}