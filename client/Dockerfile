# Build stage
FROM oven/bun:1 AS build

WORKDIR /app

# Copy root package files and workspace configuration
COPY package.json bun.lock ./
COPY tsconfig.json ./

# Copy shared package
COPY shared/ ./shared/

# Copy client package
COPY client/ ./client/

# Install dependencies
RUN bun install

# Build shared package first
RUN cd shared && bun run build

# Copy environment file if exists
RUN if [ -f client/.env ]; then cp client/.env ./client/; fi

# Build the client app
RUN cd client && bun run build

# Production stage
FROM nginx:alpine

# Copy built files to nginx
COPY --from=build /app/client/dist /usr/share/nginx/html

# Copy nginx config
COPY client/nginx.conf /etc/nginx/nginx.conf

# Expose port
EXPOSE 80

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
