#!/bin/bash

# Script untuk push Docker images ke Docker Hub
# Pastikan sudah login: docker login

echo "🚀 Pushing Sumopod Docker Images to Docker Hub..."

# Minta input username Docker Hub
read -p "Masukkan Docker Hub username Anda: " DOCKER_USERNAME

if [ -z "$DOCKER_USERNAME" ]; then
    echo "❌ Username tidak boleh kosong!"
    exit 1
fi

echo "📦 Tagging server image..."
docker tag sumopod-project-copy-backend:latest $DOCKER_USERNAME/sumopod-server:latest

echo "📦 Tagging client image..."
docker tag sumopod-project-copy-frontend:latest $DOCKER_USERNAME/sumopod-client:latest

echo "⬆️ Pushing server image..."
docker push $DOCKER_USERNAME/sumopod-server:latest

echo "⬆️ Pushing client image..."
docker push $DOCKER_USERNAME/sumopod-client:latest

echo "✅ Done! Images pushed to Docker Hub"
echo ""
echo "📋 Copy URLs ini untuk deploy ke Claw Cloud:"
echo "Server Image: $DOCKER_USERNAME/sumopod-server:latest"
echo "Client Image: $DOCKER_USERNAME/sumopod-client:latest"
echo ""
echo "📖 Baca DEPLOY-CLAW-CLOUD.md untuk panduan lengkap deploy ke Claw Cloud"
