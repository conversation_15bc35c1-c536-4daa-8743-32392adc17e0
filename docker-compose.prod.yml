version: '3.8'

services:
  # Server Service (bhvr monorepo)
  backend:
    image: your-dockerhub-username/sumopod-server:latest
    ports:
      - "8080:8080"
    environment:
      - DATABASE_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require
      - BETTER_AUTH_SECRET=9uEY2Wif0Es1Ud4ZGHBmONHjbE4bypDf
      - BETTER_AUTH_URL=https://sumopod-backend.fly.dev
      - BETTER_AUTH_TRUSTED_ORIGINS=https://cloone-sumopod.netlify.app
      - XENDIT_API_KEY=xnd_development_aRnvtZu0b1pF6huwa1dyMeOgIqk2QELKZr1WTRzKo2zqKs2z1spb4bb3du0PeV
      - XENDIT_API_URL=https://api.xendit.co/v2/invoices
      - XENDIT_CALLBACK_TOKEN=sGqcEXjShdnLd4S6DITIbOxbbKWxsPK0018WQb8JqtoHxlo9
      - PORT=8080
      - CORS_ORIGINS=http://localhost:3001,https://cloone-sumopod.netlify.app
      - CORS_ALLOW_HEADERS=Content-Type,Authorization,X-Session-Token
      - CORS_ALLOW_METHODS=GET,POST,PUT,DELETE,OPTIONS
      - APP_NAME=sumopod-backend
      - EXTERNAL_ID_PREFIX=sumopod-
    restart: unless-stopped

  # Client Service (bhvr monorepo)
  frontend:
    image: your-dockerhub-username/sumopod-client:latest
    ports:
      - "3000:80"
    depends_on:
      - backend
    restart: unless-stopped
